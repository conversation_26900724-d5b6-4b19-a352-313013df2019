import dotenv from 'dotenv';
dotenv.config();

import {GoogleGenAI} from '@google/genai';
const GEMINI_API_KEY = process.env.GEMINI_API_KEY;

const ai = new GoogleGenAI({apiKey: GEMINI_API_KEY});

async function main() {
  const response = await ai.models.generateContent({
    model: 'gemma-3-1b-it',
    contents: 'Write a 100-word poem about cellphones.',
  });
  console.log(response.text);
}

main();