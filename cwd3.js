import { GoogleGenAI } from "@google/genai";
import fs from 'fs';
import readline from 'readline';
import 'dotenv/config';
import { generateEmbedding } from './embeddingService.js';
import path from 'path';

// Function to encode the PDF to a Base64 string using chunked reading.
async function encodePdf(pdfPath) {
    try {
        return new Promise((resolve, reject) => {
            const chunks = [];
            const fileStream = fs.createReadStream(pdfPath);

            fileStream.on('data', (chunk) => {
                chunks.push(chunk);
            });

            fileStream.on('end', () => {
                const pdfBuffer = Buffer.concat(chunks);
                const base64Pdf = pdfBuffer.toString('base64');
                resolve(base64Pdf);
            });

            fileStream.on('error', (error) => {
                console.error(`Error reading file: ${error}`);
                reject(error);
            });
        });
    } catch (error) {
        console.error(`Error: ${error}`);
        return null;
    }
}

// Function to split text into chunks
function splitTextIntoChunks(text, chunkSize = 1000, overlap = 100) {
    const chunks = [];
    let i = 0;
    while (i < text.length) {
        const end = Math.min(i + chunkSize, text.length);
        chunks.push(text.substring(i, end));
        i += chunkSize - overlap;
        if (i >= text.length && end < text.length) {
            chunks.push(text.substring(end - overlap));
        } else if (i < overlap && chunks.length > 1) {
            i = end;
        }
    }
    if (chunks.length > 0 && chunks[chunks.length - 1] === "") {
        chunks.pop();
    }
    return chunks.filter(chunk => chunk.trim() !== '');
}

// Function to ingest document into Pinecone
async function ingestDocumentToPinecone(documentText, fileName) {
    const chunks = splitTextIntoChunks(documentText);
    console.log(`Split document into ${chunks.length} chunks`);

    // Directly use Pinecone client to upsert without namespace
    const { Pinecone } = await import('@pinecone-database/pinecone');
    const pc = new Pinecone({ apiKey: process.env.PINECONE_API_KEY });
    const index = pc.Index(process.env.PINECONE_INDEX_NAME);

    // Prepare all records for batch upsert
    const records = [];
    for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        try {
            const embedding = await generateEmbedding(chunk);
            const record = {
                id: `${fileName}-chunk-${i}`,
                values: embedding,
                metadata: {
                    text: chunk,
                    fileName: fileName,
                    chunkIndex: i,
                    totalChunks: chunks.length
                }
            };
            records.push(record);
        } catch (error) {
            console.error(`Error processing chunk ${i}:`, error);
        }
    }

    // Perform a single batch upsert
    if (records.length > 0) {
        await index.upsert(records);
        console.log(`Successfully stored all ${records.length} chunks in Pinecone`);
    } else {
        console.log("No valid chunks to ingest.");
    }
}

// Function to query Pinecone for relevant document chunks
async function getRelevantDocumentChunks(userQuestion, fileName) {
    try {
        // Generate embedding for the question
        const questionEmbedding = await generateEmbedding(userQuestion);

        // Directly use Pinecone client to query without namespace
        const { Pinecone } = await import('@pinecone-database/pinecone');
        const pc = new Pinecone({ apiKey: process.env.PINECONE_API_KEY });
        const index = pc.Index(process.env.PINECONE_INDEX_NAME);

        // Query Pinecone for relevant chunks
        const queryResponse = await index.query({
            vector: questionEmbedding,
            topK: 3,
            includeMetadata: true,
            includeValues: false
        });

        // Extract the text from the results
        const relevantChunks = queryResponse.matches
            .filter(match => match.metadata && match.metadata.text)
            .map(match => match.metadata.text);

        return relevantChunks.join('\n\n');
    } catch (error) {
        console.error("Error querying Pinecone:", error);
        return "";
    }
}

// Path to your PDF file.
const pdfPath = "./ready.pdf";
const fileName = path.basename(pdfPath);

// Get your API key from environment variables.
const apiKey = process.env.GEMINI_API_KEY;
if (!apiKey) {
    console.error("GEMINI_API_KEY is not set in environment variables.");
    process.exit(1);
}

// Create the GoogleGenAI client.
const client = new GoogleGenAI({ apiKey: apiKey });

// Create readline interface for interactive chat
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Conversation history storage with sliding window
const MAX_HISTORY_TURNS = 5; // Keep last 5 turns (adjust as needed)
let conversationHistory = []; // Removed system message

// Function to check if document is already ingested
async function isDocumentIngested(fileName) {
    try {
        const { Pinecone } = await import('@pinecone-database/pinecone');
        const pc = new Pinecone({ apiKey: process.env.PINECONE_API_KEY });
        const index = pc.Index(process.env.PINECONE_INDEX_NAME);

        // Create a dummy vector for the query
        const dummyVector = new Array(768).fill(0);

        // Query for any chunk from this document
        const response = await index.query({
            vector: dummyVector,
            filter: {
                fileName: fileName
            },
            topK: 1,
            includeMetadata: true
        });

        return response.matches && response.matches.length > 0;
    } catch (error) {
        console.log("Error checking if document is ingested, assuming it's not:", error);
        return false;
    }
}

// Main execution function.
async function main() {
    try {
        // --- STEP 1: Check if document is already ingested ---
        const alreadyIngested = await isDocumentIngested(fileName);

        let documentText = null;

        // --- STEP 2: Perform OCR and ingest only if not already ingested ---
        if (!alreadyIngested) {
            console.log("Document not found in vector store. Processing OCR...");

            const base64Pdf = await encodePdf(pdfPath);
            if (!base64Pdf) {
                console.error("Could not encode PDF. Exiting.");
                return;
            }

            console.log("Processing OCR...");
            const ocrResponse = await client.ocr.process({
                model: "mistral-ocr-latest",
                document: {
                    type: "document_url",
                    documentUrl: "data:application/pdf;base64," + base64Pdf
                }
            });

            // The extracted text content is in ocrResponse.pages[].markdown.
            // We need to concatenate the text from all pages into a single string.
            documentText = ocrResponse.pages.map(page => page.markdown).join('\n');
            console.log("OCR complete. Document text extracted.\n");

            console.log("Ingesting document into Pinecone vector store...");
            await ingestDocumentToPinecone(documentText, fileName);
            console.log("Document ingestion complete!\n");
        } else {
            console.log("Document already ingested. Skipping OCR and ingestion.\n");
        }

        // Start interactive chat
        console.log("PDF Document Chat - Type 'exit' to quit\n");

        // Ask questions in a loop
        askQuestion(fileName);

    } catch (error) {
        console.error("Error:", error);
        rl.close();
    }
}

// Function to ask questions interactively
async function askQuestion(fileName) {
    rl.question('Ask a question about the document: ', async (userQuestion) => {
        if (userQuestion.toLowerCase() === 'exit') {
            console.log('Goodbye!');
            rl.close();
            return;
        }

        try {
            console.log(`\nProcessing your question: "${userQuestion}"...`);

            // Get relevant document chunks from Pinecone
            const relevantText = await getRelevantDocumentChunks(userQuestion, fileName);

            // Add user question to conversation history
            conversationHistory.push({
                role: 'user',
                content: `Document text: ${relevantText}\n\nQuestion: ${userQuestion}`
            });

            // Prepare the prompt for the chat model with conversation history
            const chatResponseStream = await client.models.generateContentStream({
                model: 'gemini-2.5-flash',
                contents: conversationHistory.map(msg => ({
                    role: msg.role,
                    parts: [{ text: msg.content }]
                }))
            });

            // Display the answer as it's being generated
            console.log("\nAnswer:");
            let fullResponse = "";
            for await (const chunk of chatResponseStream) {
                if (chunk.text) {
                    process.stdout.write(chunk.text);
                    fullResponse += chunk.text;
                }
            }
            console.log(); // Add newline after the response

            // Add model response to conversation history
            conversationHistory.push({
                role: 'model',
                content: fullResponse
            });

            // Apply sliding window to keep only the last N turns
            if (conversationHistory.length > MAX_HISTORY_TURNS) {
                // Keep last N turns
                conversationHistory = conversationHistory.slice(-MAX_HISTORY_TURNS);
            }

            // Ask another question
            askQuestion(fileName);
        } catch (error) {
            console.error("Error:", error);
            askQuestion(fileName);
        }
    });
}

// Run the main function.
main();
